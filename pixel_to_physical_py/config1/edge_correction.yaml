# 边缘校正配置文件
# 用于修正边缘区域z方向距离偏大的问题
edge_correction:
  enabled: true

  # 校正强度参数 - 用于减小边缘区域的z距离误差
  strength: 0.15  # 基础校正强度，范围 0.0-0.3（过大会过度校正）
  
  # 图像参数
  image_width: 1280
  image_height: 720
  
  # 权重配置
  weights:
    x_direction: 0.8  # X方向（水平）权重，影响更大
    y_direction: 0.2  # Y方向（垂直）权重，影响较小
  
  # 分区校正（可选，用于更精细的校正）
  zone_correction:
    enabled: false
    zones:
      # 左边缘区域 - z距离偏大，需要减小
      left_edge:
        x_range: [0, 800]
        correction_factor: 0.6  # 小于1表示减小距离
      # 右边缘区域 - z距离偏大，需要减小
      right_edge:
        x_range: [800, 1280]
        correction_factor: 0.6  # 小于1表示减小距离
      # 上边缘区域
      top_edge:
        y_range: [400, 500]
        correction_factor: 0.8
      # 下边缘区域
      bottom_edge:
        y_range: [500, 720]
        correction_factor: 0.8
        
  # 调试选项
  debug:
    print_corrections: true  # 是否打印校正信息
    min_correction_threshold: 1.0  # 最小校正阈值，校正因子偏离1.0超过此值时打印
    
  # 校正算法选择
  algorithm: "radial"  # "radial" 或 "linear" 或 "zone"
  
  # 径向校正参数（当algorithm为"radial"时使用）
  radial_correction:
    center_weight: 0.9    # 中心区域权重（无校正）
    edge_weight: 0.7      # 边缘区域权重（用于减小距离）
    falloff_power: 2.0    # 衰减指数（控制校正强度随距离的变化）
