version: 1.0.0
camera_parameters:
  intrinsics_and_distortion:
  - 681.063986190517
  - 679.8179394356114
  - 629.4832998733726
  - 365.3302488092959
  - 0.17191654840662463
  - -0.24363732008360764
  - -0.0025997938933930366
  - 0.0004994316580192718
  - 0.11357479957296686
  - 0.0
  - 0.0
  - 0.0
  camera_matrix:
    fx: 681.063986190517
    fy: 679.8179394356114
    cx: 629.4832998733726
    cy: 365.3302488092959
  distortion_coefficients:
    radial:
      k1: 0.17191654840662463
      k2: -0.24363732008360764
      k3: 0.11357479957296686
      k4: 0.0
      k5: 0.0
      k6: 0.0
    tangential:
      p1: -0.0025997938933930366
      p2: 0.0004994316580192718
image_properties:
  width: 1280
  height: 720
  channels: 3
  pixel_format: BGR
calibration_quality:
  mean_reprojection_error: 0.024841749632214943
  calibration_date: '2025-06-12'
  calibration_method: chessboard
  num_calibration_images: 90
chessboard_info:
  board_size:
  - 11
  - 8



# version: 1.0.0
# camera_parameters:
#   intrinsics_and_distortion:
#   - 686.5413642501873
#   - 685.6969659580126
#   - 625.3961040606996
#   - 367.4070587316423
#   - 0.17609031322779517
#   - -0.24735149732535058
#   - -0.0009271104690397132
#   - -9.418877377042286e-06
#   - 0.1169591153755648
#   - 0.0
#   - 0.0
#   - 0.0
#   camera_matrix:
#     fx: 686.5413642501873
#     fy: 685.6969659580126
#     cx: 625.3961040606996
#     cy: 367.4070587316423
#   distortion_coefficients:
#     radial:
#       k1: 0.17609031322779517
#       k2: -0.24735149732535058
#       k3: 0.1169591153755648
#       k4: 0.0
#       k5: 0.0
#       k6: 0.0
#     tangential:
#       p1: -0.0009271104690397132
#       p2: -9.418877377042286e-06
# image_properties:
#   width: 1280
#   height: 720
#   channels: 3
#   pixel_format: BGR
# calibration_quality:
#   mean_reprojection_error: 0.02606467005531868
#   calibration_date: '2025-06-12'
#   calibration_method: chessboard
#   num_calibration_images: 72
# chessboard_info:
#   board_size:
#   - 11
#   - 8
