import yaml
import numpy as np
from dataclasses import dataclass
from typing import Tuple
from src.pixel_converter import PixelConverter

@dataclass
class BBox:
    x1: int
    y1: int
    x2: int
    y2: int

@dataclass
class DetectionResult:
    bbox: BBox
    label: str
    confidence: float
    physical_distance: float = 0.0
    left_distance: float = 0.0
    right_distance: float = 0.0

class DetectionProcessor:
    def __init__(self):
        self.size_ranges_config = None
        # 边缘校正配置
        self.edge_correction_config = None
        self.edge_correction_enabled = True
        self.edge_correction_strength = 0.15  # 校正强度，可调整
        self.image_width = 1280  # 图像宽度
        self.image_height = 720  # 图像高度
        self.x_weight = 0.8  # X方向权重
        self.y_weight = 0.2  # Y方向权重
        self.algorithm = "radial"  # 校正算法
        self.debug_enabled = True  # 调试输出

    def set_size_ranges_config(self, config):
        """Set the size ranges configuration from YAML."""
        self.size_ranges_config = config

    def load_edge_correction_config(self, config_path):
        """从YAML文件加载边缘校正配置"""
        try:
            with open(config_path, 'r') as f:
                self.edge_correction_config = yaml.safe_load(f)

            # 应用配置
            edge_config = self.edge_correction_config.get('edge_correction', {})
            self.edge_correction_enabled = edge_config.get('enabled', True)
            self.edge_correction_strength = edge_config.get('strength', 0.15)
            self.image_width = edge_config.get('image_width', 1280)
            self.image_height = edge_config.get('image_height', 720)

            weights = edge_config.get('weights', {})
            self.x_weight = weights.get('x_direction', 0.8)
            self.y_weight = weights.get('y_direction', 0.2)

            self.algorithm = edge_config.get('algorithm', 'radial')

            debug_config = edge_config.get('debug', {})
            self.debug_enabled = debug_config.get('print_corrections', True)
            self.min_correction_threshold = debug_config.get('min_correction_threshold', 1.05)

            print(f"边缘校正配置加载成功: 算法={self.algorithm}, 强度={self.edge_correction_strength}")

        except Exception as e:
            print(f"加载边缘校正配置失败: {e}")
            print("使用默认配置")

    def set_edge_correction_params(self, enabled=True, strength=0.15, image_width=1280, image_height=720):
        """设置边缘校正参数"""
        self.edge_correction_enabled = enabled
        self.edge_correction_strength = strength
        self.image_width = image_width
        self.image_height = image_height

    def calculate_edge_correction_factor(self, pixel_x, pixel_y):
        """
        计算边缘校正因子

        Args:
            pixel_x: 像素X坐标
            pixel_y: 像素Y坐标

        Returns:
            correction_factor: 校正因子，用于修正距离
        """
        if not self.edge_correction_enabled:
            return 1.0

        if self.algorithm == "radial":
            return self._calculate_radial_correction(pixel_x, pixel_y)
        elif self.algorithm == "linear":
            return self._calculate_linear_correction(pixel_x, pixel_y)
        elif self.algorithm == "zone":
            return self._calculate_zone_correction(pixel_x, pixel_y)
        else:
            return self._calculate_linear_correction(pixel_x, pixel_y)

    def _calculate_linear_correction(self, pixel_x, pixel_y):
        """线性边缘校正 - 修正边缘区域的z距离误差"""
        center_x = self.image_width / 2
        center_y = self.image_height / 2

        # X方向的边缘因子（主要影响z距离误差）
        x_edge_factor = abs(pixel_x - center_x) / center_x

        # Y方向的边缘因子（次要影响）
        y_edge_factor = abs(pixel_y - center_y) / center_y

        # 综合边缘因子，X方向权重更大（因为水平方向对z距离影响更大）
        combined_edge_factor = self.x_weight * x_edge_factor + self.y_weight * y_edge_factor

        # 限制边缘因子范围
        combined_edge_factor = min(combined_edge_factor, 1.0)

        # 计算校正因子：
        # 如果边缘区域z距离偏大，则校正因子应该小于1（减小距离）
        # 如果边缘区域z距离偏小，则校正因子应该大于1（增大距离）
        # 根据您的观察，边缘区域z距离偏大，所以使用减小校正
        correction_factor = 1.0 - combined_edge_factor * self.edge_correction_strength

        # 确保校正因子不会过小
        correction_factor = max(correction_factor, 0.7)

        return correction_factor

    def _calculate_radial_correction(self, pixel_x, pixel_y):
        """径向边缘校正（考虑到图像中心的径向距离）- 修正z距离误差"""
        center_x = self.image_width / 2
        center_y = self.image_height / 2

        # 计算到中心的径向距离
        dx = pixel_x - center_x
        dy = pixel_y - center_y
        radial_distance = np.sqrt(dx**2 + dy**2)

        # 最大可能的径向距离
        max_radial = np.sqrt(center_x**2 + center_y**2)

        # 归一化径向距离
        normalized_radial = radial_distance / max_radial

        # 获取径向校正配置
        radial_config = self.edge_correction_config.get('edge_correction', {}).get('radial_correction', {}) if self.edge_correction_config else {}
        falloff_power = radial_config.get('falloff_power', 2.0)
        edge_weight = radial_config.get('edge_weight', 0.8)  # 降低权重，因为要减小而不是增大

        # 径向校正：距离中心越远，z距离误差越大，需要减小校正
        # 使用减法而不是加法
        correction_factor = 1.0 - (normalized_radial ** falloff_power) * self.edge_correction_strength * edge_weight

        # 确保校正因子不会过小
        correction_factor = max(correction_factor, 0.6)

        return correction_factor

    def _calculate_zone_correction(self, pixel_x, pixel_y):
        """分区域校正"""
        if not self.edge_correction_config:
            return self._calculate_linear_correction(pixel_x, pixel_y)

        zone_config = self.edge_correction_config.get('edge_correction', {}).get('zone_correction', {})
        if not zone_config.get('enabled', False):
            return self._calculate_linear_correction(pixel_x, pixel_y)

        zones = zone_config.get('zones', {})
        correction_factor = 1.0

        # 检查各个区域
        for zone_name, zone_params in zones.items():
            if 'x_range' in zone_params:
                x_min, x_max = zone_params['x_range']
                if x_min <= pixel_x <= x_max:
                    correction_factor = max(correction_factor, zone_params.get('correction_factor', 1.0))

            if 'y_range' in zone_params:
                y_min, y_max = zone_params['y_range']
                if y_min <= pixel_y <= y_max:
                    correction_factor = max(correction_factor, zone_params.get('correction_factor', 1.0))

        return correction_factor

    def apply_distance_correction(self, distance, pixel_x, pixel_y):
        """
        对距离应用边缘校正

        Args:
            distance: 原始距离
            pixel_x: 像素X坐标
            pixel_y: 像素Y坐标

        Returns:
            corrected_distance: 校正后的距离
        """
        correction_factor = self.calculate_edge_correction_factor(pixel_x, pixel_y)
        corrected_distance = distance * correction_factor

        # 打印校正信息（调试用）
        min_threshold = getattr(self, 'min_correction_threshold', 0.95)  # 调整阈值，因为现在校正因子可能小于1
        if self.debug_enabled and abs(correction_factor - 1.0) > (1.0 - min_threshold):
            correction_percent = (correction_factor - 1.0) * 100
            print(f"边缘校正[{self.algorithm}]: 位置({pixel_x:.0f}, {pixel_y:.0f}), "
                  f"校正因子: {correction_factor:.3f} ({correction_percent:+.1f}%), "
                  f"原始距离: {distance:.2f}cm -> 校正距离: {corrected_distance:.2f}cm")

        return corrected_distance

    def analyze_edge_bias(self, detection_results):
        """
        分析边缘区域的距离偏差模式，用于调整校正参数

        Args:
            detection_results: 检测结果列表

        Returns:
            bias_analysis: 偏差分析结果
        """
        if not detection_results:
            return None

        center_x = self.image_width / 2
        edge_distances = []
        center_distances = []

        for det in detection_results:
            if det.physical_distance <= 0:
                continue

            bbox_center_x = (det.bbox.x1 + det.bbox.x2) / 2
            distance_from_center = abs(bbox_center_x - center_x) / center_x

            if distance_from_center > 0.6:  # 边缘区域
                edge_distances.append(det.physical_distance)
            elif distance_from_center < 0.3:  # 中心区域
                center_distances.append(det.physical_distance)

        if len(edge_distances) > 0 and len(center_distances) > 0:
            avg_edge = np.mean(edge_distances)
            avg_center = np.mean(center_distances)
            bias_ratio = avg_edge / avg_center

            print(f"边缘偏差分析: 中心平均距离={avg_center:.1f}cm, "
                  f"边缘平均距离={avg_edge:.1f}cm, 偏差比={bias_ratio:.3f}")

            return {
                'edge_avg': avg_edge,
                'center_avg': avg_center,
                'bias_ratio': bias_ratio,
                'suggested_strength': max(0.05, min(0.3, abs(bias_ratio - 1.0)))
            }

        return None

    def is_size_reasonable_for_label(self, label: str, max_size: float, min_size: float) -> bool:
        """Check if the object size is reasonable for its label."""
        try:
            object_config = self.size_ranges_config["objects"].get(label, self.size_ranges_config["default"])
            
            config_max_size = object_config["max_size"]
            config_min_size = object_config["min_size"]
            description = object_config["description"]

            if max_size > config_max_size or min_size < config_min_size:
                print(f"警告：{description}")
                return False
            return True
        except Exception as e:
            print(f"Error checking size ranges: {str(e)}")
            return False

    def calculate_target_size(self, bbox: BBox, converter: PixelConverter) -> Tuple[float, float, float, float]:
        """Calculate the target size and return size_y, size_x, z1, z2."""
        try:
            # Get physical coordinates for the corners
            x1, y1 = converter.query_physical_location(bbox.x1, bbox.y2)  # bottom left
            x2, y2 = converter.query_physical_location(bbox.x2, bbox.y2)  # bottom right
            # x3, y3 = converter.query_physical_location(bbox.x1, bbox.y1)  # top left

            # Print physical coordinates
            print(f"像素坐标 ({bbox.x1}, {bbox.y2}) -> 物理坐标 ({x1:.3f}, {y1:.3f})")
            print(f"像素坐标 ({bbox.x2}, {bbox.y2}) -> 物理坐标 ({x2:.3f}, {y2:.3f})")
            # print(f"像素坐标 ({bbox.x1}, {bbox.y1}) -> 物理坐标 ({x3:.3f}, {y3:.3f})")

            # Calculate lengths
            bottom_length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
            # left_length = np.sqrt((x3 - x1)**2 + (y3 - y1)**2)

            print("----------------------------------------")
            print(f"下边长度: {bottom_length:.3f} cm")
            # print(f"左边宽度: {left_length:.3f} cm")
            print("----------------------------------------")

            return bottom_length, x1, x2

        except Exception as e:
            print(f"Error calculating target size: {str(e)}")
            return 0.0, 0.0, 0.0, 0.0

    def is_detection_reasonable(self, det: DetectionResult, table_path: str) -> Tuple[bool, float, float]:
        """Check if the detection result is reasonable."""
        if det is None:
            print("检测结果为空!")
            return False, 0.0, 0.0

        converter = PixelConverter(table_path)

        # Check if box is in image range
        if not converter.is_valid_pixel_coordinate(det.bbox.x1, det.bbox.y2) or \
           not converter.is_valid_pixel_coordinate(det.bbox.x2, det.bbox.y2):
            print(det.bbox.x1, det.bbox.y1, det.bbox.x2, det.bbox.y2)
            print("警告：检测框超出图像范围")
            return False, 0.0, 0.0

        # Check if box is too small
        box_area = (det.bbox.x2 - det.bbox.x1) * (det.bbox.y2 - det.bbox.y1)
        if box_area < 10:
            print(f"警告：检测框面积过小 ({box_area} 像素)")
            return False, 0.0, 0.0

        # Calculate target size
        size_y, z1, z2 = self.calculate_target_size(det.bbox, converter)
        if size_y == 0.0:
            print("无法计算目标尺寸")
            return False, 0.0, 0.0

        # Check if distance is reasonable
        # distance = abs(size_x)
        distance = z1
        if distance > PixelConverter.X_MAX:
            print(f"警告：目标距离过远 ({distance:.1f} cm)")
            return False, 0.0, 0.0

        # Print target size
        print(f"\n目标尺寸:")
        print(f"长度: {size_y:.3f} cm")
        # print(f"宽度: {size_x:.3f} cm")

        # Check size against label
        # max_size = max(size_x, size_y)
        # min_size = min(size_x, size_y)
        # if not self.is_size_reasonable_for_label(det.label, max_size, min_size):
        #     return False, 0.0, 0.0

        return True, z1, z2

    def process_detection_result(self, det: DetectionResult, table_path: str):
        """Process the detection result and update physical distances."""
        print("\n处理检测结果:")
        print(f"标签: {det.label}, 置信度: {det.confidence}")
        print("----------------------------------------")

        is_reasonable, z1, z2 = self.is_detection_reasonable(det, table_path)
        if not is_reasonable:
            print("检测结果不合理，跳过处理")
            return

        # 计算检测框中心点用于边缘校正
        center_x = (det.bbox.x1 + det.bbox.x2) / 2
        # center_y = (det.bbox.y1 + det.bbox.y2) / 2
        center_y = det.bbox.y2

        # 应用边缘校正
        corrected_z1 = self.apply_distance_correction(z1, det.bbox.x1, det.bbox.y2)
        corrected_z2 = self.apply_distance_correction(z2, det.bbox.x2, det.bbox.y2)
        # corrected_avg = self.apply_distance_correction((z1 + z2) / 2, center_x, center_y)

        # Update detection result with corrected physical distances
        det.left_distance = corrected_z1
        det.right_distance = corrected_z2
        # det.physical_distance = corrected_avg
        det.physical_distance = corrected_z1