import cv2
import numpy as np
import os
import torch
import yaml
import sys
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path
from dataclasses import dataclass,field

from tools.inference import Detector, MultiTaskDetector
from econn.utils.config import cfg, update_config

# 添加pixel_to_physical_py路径
sys.path.append('pixel_to_physical_py')
from src.detection_processor import DetectionProcessor, DetectionResult, BBox

@dataclass
class AppConfig:
    demo_source: str = "webcam"
    camera_id: int = 0
    config_path: str = "exp/GFL_0503_gpu2/train_config.yml"
    model_path: str = "exp/GFL_0503_gpu2/model_best_score/model_best_score.pth"
    size_ranges_config: str = "pixel_to_physical_py/config/size_ranges.yaml"
    distance_table_path: str = "pixel_to_physical_py/config/distance_table"
    calibration_mapx_path: str = "pixel_to_physical_py/config/mapx"
    calibration_mapy_path: str = "pixel_to_physical_py/config/mapy"
    calib_yaml_path: str = "pixel_to_physical_py/config/calib_intrix_new.yaml"
    # output_dir: str = "imgs/"
    imgx_range: Tuple[int, int] = (30, 1250)
    imgy_range: Tuple[int, int] = (420, 700)
    img_wh: Tuple[int, int] = field(default_factory=lambda: (720, 1280))
    class_names: List[str] = field(
        default_factory=lambda: [
            'Trash can', 'Charging dock', 'Cleaning cloth', 'Rug', 'Shoes',
            'Wire', 'Sliding rail', 'Wheels'
        ]
    )
    class_name_mapping: Dict[str, str] = field(
        default_factory=lambda: {
            'trash can': 'bin',
            'charging dock': 'seatbase',
            'cleaning cloth': 'cloth',
            'rug': 'rug',
            'shoes': 'shoe',
            'wire': 'wire',
            'sliding rail': 'rail',
            'wheels': 'wheel'
        }
    )

class DistanceDetectionApp:
    def __init__(self, config: AppConfig):
        self.config = config
        self.detector = None
        self.physical_processor = None
        self.camera_matrix = None
        self.dist_coeffs = None
        self.calibration_mapx = None
        self.calibration_mapy = None
        self.use_calibration_maps = False
        self.video_capture = None
        
        # 创建输出目录
        # os.makedirs(self.config.output_dir, exist_ok=True)
        
        # 初始化模型和处理器
        self._initialize_models()
        self._initialize_physical_processor()
        self._load_camera_calibration()
        
    def _initialize_models(self) -> None:
        """初始化检测模型"""
        update_config(cfg, self.config.config_path)
        
        if cfg.model.architecture == 'MultiTask':
            self.detector = MultiTaskDetector(cfg, self.config.model_path, device='cuda:0')
        else:
            self.detector = Detector(cfg, self.config.model_path, 'cuda:0')
            
        torch.backends.cudnn.enabled = True
        torch.backends.cudnn.benchmark = True
    
    def _initialize_physical_processor(self) -> None:
        """初始化物理距离处理器"""
        try:
            with open(self.config.size_ranges_config, 'r') as f:
                size_config = yaml.safe_load(f)

            self.physical_processor = DetectionProcessor()
            self.physical_processor.set_size_ranges_config(size_config)

            # 尝试加载标定映射表
            try:
                self.calibration_mapx = np.fromfile(
                    self.config.calibration_mapx_path, 
                    dtype=np.float32
                ).reshape(self.config.img_wh[0], self.config.img_wh[1])
                self.calibration_mapy = np.fromfile(
                    self.config.calibration_mapy_path, 
                    dtype=np.float32
                ).reshape(self.config.img_wh[0], self.config.img_wh[1])
                self.use_calibration_maps = True
                print("标定映射表加载成功")
            except Exception as map_error:
                print(f"标定映射表加载失败: {map_error}")
                print("将使用传统去畸变方法")
                self.use_calibration_maps = False

            print("物理距离检测处理器初始化成功")
        except Exception as e:
            print(f"物理距离检测处理器初始化失败: {e}")
            self.physical_processor = None
    
    def _load_camera_calibration(self) -> None:
        """加载相机标定参数"""
        try:
            with open(self.config.calib_yaml_path, "r") as f:
                calib = yaml.safe_load(f)

            cam = calib["camera_parameters"]
            fx = cam["camera_matrix"]["fx"]
            fy = cam["camera_matrix"]["fy"]
            cx = cam["camera_matrix"]["cx"]
            cy = cam["camera_matrix"]["cy"]

            self.camera_matrix = np.array([
                [fx, 0, cx],
                [0, fy, cy],
                [0,  0,  1]
            ], dtype=np.float64)

            radial = cam["distortion_coefficients"]["radial"]
            tangential = cam["distortion_coefficients"]["tangential"]

            self.dist_coeffs = np.array([
                radial["k1"],
                radial["k2"],
                tangential["p1"],
                tangential["p2"],
                radial["k3"]
            ], dtype=np.float64)
            
            print("相机标定参数加载成功")
        except Exception as e:
            print(f"加载相机标定参数失败: {e}")
            self.camera_matrix = None
            self.dist_coeffs = None
    
    def _preprocess_image(self, img: np.ndarray) -> np.ndarray:
        """预处理图像（去畸变）"""
        if self.use_calibration_maps and self.calibration_mapx is not None and self.calibration_mapy is not None:
            # 使用标定过程生成的映射表
            processed_img = cv2.remap(img, self.calibration_mapx, self.calibration_mapy, cv2.INTER_LINEAR)
            print("使用标定映射表处理图像")
        elif self.camera_matrix is not None and self.dist_coeffs is not None:
            # 使用传统去畸变方法
            h, w = img.shape[:2]
            newcameramtx, roi = cv2.getOptimalNewCameraMatrix(
                self.camera_matrix, self.dist_coeffs, (w, h), alpha=0, newImgSize=(w, h))
            mapx, mapy = cv2.initUndistortRectifyMap(
                self.camera_matrix, self.dist_coeffs, None, newcameramtx, (w, h), cv2.CV_32FC1)
            processed_img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
            print("使用传统去畸变处理图像")
        else:
            processed_img = img.copy()
            print("未进行图像去畸变处理")
            
        return processed_img
    
    def _convert_dets_to_detection_results(self, dets: Union[Dict, List], class_names: List[str]) -> List[DetectionResult]:
        """
        将检测器输出的dets格式转换为DetectionResult列表

        Args:
            dets: 检测器输出的检测结果
            class_names: 类别名称列表

        Returns:
            转换后的检测结果列表
        """
        detection_results = []

        if not dets or len(dets) == 0:
            return detection_results

        # dets[0] 是第一张图片的检测结果
        det_dict = dets[0] if isinstance(dets, list) else dets

        for label_id, bboxes in det_dict.items():
            if not bboxes:  # 如果该类别没有检测结果
                continue

            # 获取类别名称
            if label_id <= len(class_names):
                label_name = class_names[label_id - 1]  # label_id 是1-based
            else:
                label_name = f"class_{label_id}"

            # 转换每个检测框
            for bbox in bboxes:
                if len(bbox) >= 5:  # 确保有足够的元素 [x1, y1, x2, y2, confidence]
                    x1, y1, x2, y2, confidence = bbox[:5]

                    # 过滤置信度过低的检测结果
                    if confidence < 0.3:
                        continue

                    # 创建BBox对象
                    bbox_obj = BBox(
                        x1=int(x1),
                        y1=int(y1),
                        x2=int(x2),
                        y2=int(y2)
                    )

                    # 映射类别名称
                    mapped_label = self.config.class_name_mapping.get(label_name.lower(), label_name.lower())

                    # 创建DetectionResult对象
                    det_result = DetectionResult(
                        bbox=bbox_obj,
                        label=mapped_label,
                        confidence=float(confidence)
                    )

                    detection_results.append(det_result)

        return detection_results
    
    def _show_result_with_distance(self, img: np.ndarray, dets: Union[Dict, List], 
                                 detection_results: List[DetectionResult], 
                                 class_names: List[str], score_thres: float = 0.3) -> np.ndarray:
        """
        显示检测结果并包含物理距离信息，不同类别使用不同颜色

        Args:
            img: 原始图像
            dets: 原始检测结果
            detection_results: 包含物理距离的检测结果列表
            class_names: 类别名称列表
            score_thres: 置信度阈值

        Returns:
            绘制了检测框和距离信息的图像
        """
        # 为不同类别定义不同颜色
        colors = [
            (255, 0, 0),    # 红色 - class 1
            (0, 255, 0),    # 绿色 - class 2
            (0, 0, 255),    # 蓝色 - class 3
            (255, 255, 0),  # 青色 - class 4
            (255, 0, 255),  # 紫色 - class 5
            (0, 255, 255),  # 黄色 - class 6
            (255, 128, 0),  # 橙色 - class 7
            (128, 0, 255),  # 紫红色 - class 8
            (0, 255, 128),  # 春绿色 - class 9
            (128, 255, 0)   # 黄绿色 - class 10
        ]
        
        # 在去畸变图像上绘制范围框
        x_min, x_max = self.config.imgx_range
        y_min, y_max = self.config.imgy_range
        
        # 绘制矩形框 (红色，线宽2)
        cv2.rectangle(img, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)
        
        # 创建一个字典来快速查找物理距离信息
        distance_info = {
            (det_result.bbox.x1, det_result.bbox.y1, det_result.bbox.x2, det_result.bbox.y2): det_result 
            for det_result in detection_results
        }
        
        # 绘制检测框和距离信息
        if isinstance(dets, list) and len(dets) > 0:
            det_dict = dets[0]
        else:
            det_dict = dets

        for label_id, bboxes in det_dict.items():
            if not bboxes:
                continue

            # 为当前类别选择颜色
            color_idx = (label_id - 1) % len(colors)
            color = colors[color_idx]

            for bbox in bboxes:
                if len(bbox) >= 5 and bbox[4] > score_thres:
                    x1, y1, x2, y2, confidence = bbox[:5]
                    x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                    
                    if confidence < 0.3:
                        continue
                        
                    # 绘制检测框
                    cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)

                    # 获取类别名称
                    if label_id <= len(class_names):
                        label_name = class_names[label_id - 1]
                    else:
                        label_name = f"class_{label_id}"

                    key = (x1, y1, x2, y2)
                    det_result = distance_info.get(key)

                    # 准备显示文本
                    if det_result and det_result.physical_distance > 0:
                        text = f"{label_name}: {det_result.physical_distance:.1f}cm"
                    else:
                        text = f"{label_name}: {confidence:.2f}"

                    # 绘制文本背景
                    text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                    cv2.rectangle(img, (x1, y1-25), (x1 + text_size[0], y1), color, -1)

                    # 绘制文本
                    cv2.putText(img, text, (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
                    cv2.putText(img, f"({x1},{y2})", (x1, y2+5), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
                    cv2.putText(img, f"({x2},{y2})", (x2, y2+5), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

        return img
    
    def _initialize_video_capture(self) -> None:
        """初始化视频捕获"""
        if self.config.demo_source == 'webcam':
            self.video_capture = cv2.VideoCapture(self.config.camera_id, cv2.CAP_V4L2)
            # 指定分辨率和编码格式
            self.video_capture.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
            self.video_capture.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.img_wh[1])
            self.video_capture.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.img_wh[0])
            
            if not self.video_capture.isOpened():
                raise RuntimeError("无法打开摄像头")
        else:
            # 处理视频文件
            self.video_capture = cv2.VideoCapture(self.config.demo_source)
            if not self.video_capture.isOpened():
                raise RuntimeError(f"无法打开视频文件: {self.config.demo_source}")
    
    def run(self) -> None:
        """运行主应用程序"""
        print('Press "Esc", "q" or "Q" to exit.')
        
        try:
            self._initialize_video_capture()
            count = 0
            
            while True:
                ret_val, img = self.video_capture.read()
                if not ret_val:
                    print("无法读取帧")
                    break
                    
                # 预处理图像
                processed_img = self._preprocess_image(img)
                
                # 执行检测
                dets, meta = self.detector.inference(processed_img)
                
                # 处理物理距离计算
                detection_results = []
                if self.physical_processor is not None:
                    try:
                        detection_results = self._convert_dets_to_detection_results(dets, self.config.class_names)
                        print(f"\n检测到 {len(detection_results)} 个目标")

                        for i, det_result in enumerate(detection_results):
                            print(f"\n--- 处理目标 {i+1}: {det_result.label} ---")
                            self.physical_processor.process_detection_result(
                                det_result, self.config.distance_table_path)
                            
                            if det_result.physical_distance > 0:
                                print(f"目标 {det_result.label}:")
                                print(f"  物理距离: {det_result.physical_distance:.2f} cm")
                                print(f"  左下角距离: {det_result.left_distance:.2f} cm")
                                print(f"  右下角距离: {det_result.right_distance:.2f} cm")
                            else:
                                print(f"目标 {det_result.label}: 无法计算物理距离")
                    except Exception as e:
                        print(f"物理距离计算出错: {e}")

                # 显示结果
                img_with_distance = self._show_result_with_distance(
                    meta['raw_img'].copy(), dets, detection_results, self.config.class_names, 0.3)
                cv2.imshow('det_with_distance', img_with_distance)
                count += 1
                
                # 检查退出键
                if cv2.waitKey(1) & 0xFF in [27, ord('q'), ord('Q')]:
                    break
                    
        finally:
            if self.video_capture is not None:
                self.video_capture.release()
            cv2.destroyAllWindows()

if __name__ == "__main__":
    config = AppConfig()
    app = DistanceDetectionApp(config)
    app.run()